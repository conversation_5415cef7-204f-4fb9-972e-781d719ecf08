name: Publish Python 🐍 distribution 📦 to PyPI and TestPyPI
on:
  push:
    branches:
    - main
    tags:
    - '*'
  workflow_dispatch:
jobs:
  build-windows:
    name: Build distribution Windows 📦
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [windows-2022]
        python-version: ["3.8", "3.9", "3.10", "3.11", "3.12"]

    steps:
    - uses: actions/checkout@v4
      with:
        submodules: recursive
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install pypa/build
      run: >-
        python3 -m
        pip install
        build
        --user
    - name: Build a binary wheel and a source tarball (windows)
      if: runner.os == 'Windows'
      run: |
        $env:KUN_USE_GIT_VERSION=${{ startsWith(github.ref, 'refs/tags/') && '0' || '1' }}
        python3 -m build --wheel
    - name: Store the distribution packages
      uses: actions/upload-artifact@v4
      with:
        name: python-package-distributions-${{ matrix.os }}${{ matrix.python-version }}
        path: dist/
  build-linux:
    name: Build distribution Linux 📦
    runs-on: ubuntu-22.04
    strategy:
      matrix:
        include:
          - python-version: "3.8"
            cp-tag: "cp38"
          - python-version: "3.9"
            cp-tag: "cp39"
          - python-version: "3.10"
            cp-tag: "cp310"
          - python-version: "3.11"
            cp-tag: "cp311"
          - python-version: "3.12"
            cp-tag: "cp312"
    steps:
    - uses: actions/checkout@v4
      with:
        submodules: recursive
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"
    - name: Install cibuildwheel
      run: >-
        python3 -m
        pip install
        cibuildwheel
        --user
    - name: Build a binary wheel and a source tarball (linux)
      run: |
        cibuildwheel --platform linux --output-dir wheelhouse
      env:
          CIBW_BUILD: "${{ matrix.cp-tag }}-manylinux_x86_64"
          CIBW_BUILD_FRONTEND: "build"
          CIBW_MANYLINUX_X86_64_IMAGE: "ghcr.io/menooker/kunquant:main"
          CIBW_ENVIRONMENT: "KUN_USE_GIT_VERSION=${{ startsWith(github.ref, 'refs/tags/') && '0' || '1' }}"
    - name: Store the distribution packages
      uses: actions/upload-artifact@v4
      with:
        name: python-package-distributions-ubuntu-22.04${{ matrix.python-version }}
        path: wheelhouse/*.whl
  publish-to-pypi:
    name: >-
      Publish Python 🐍 distribution 📦 to PyPI
    if: startsWith(github.ref, 'refs/tags/')  # only publish to PyPI on tag pushes
    needs:
    - build-windows
    - build-linux
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.8", "3.9", "3.10", "3.11", "3.12"]
    environment:
      name: pypi
      url: https://pypi.org/p/KunQuant  # Replace <package-name> with your PyPI project name
    permissions:
      id-token: write  # IMPORTANT: mandatory for trusted publishing
    steps:
    - name: Download all the dists
      uses: actions/download-artifact@v4
      with:
        name: python-package-distributions-ubuntu-22.04${{ matrix.python-version }}
        path: dist/
    - name: Download all windows the dists
      uses: actions/download-artifact@v4
      with:
        name: python-package-distributions-windows-2022${{ matrix.python-version }}
        path: dist/
    - name: Publish distribution 📦 to PyPI
      uses: pypa/gh-action-pypi-publish@release/v1
  publish-to-testpypi:
    name: Publish Python 🐍 distribution 📦 to TestPyPI
    needs:
    - build-windows
    - build-linux
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.8", "3.9", "3.10", "3.11", "3.12"]
    environment:
      name: testpypi
      url: https://test.pypi.org/p/KunQuant
    permissions:
      id-token: write  # IMPORTANT: mandatory for trusted publishing
    steps:
    - name: Download all the dists
      uses: actions/download-artifact@v4
      with:
        name: python-package-distributions-ubuntu-22.04${{ matrix.python-version }}
        path: dist/
    - name: Download all windows the dists
      uses: actions/download-artifact@v4
      with:
        name: python-package-distributions-windows-2022${{ matrix.python-version }}
        path: dist/
    - name: Publish distribution 📦 to TestPyPI
      uses: pypa/gh-action-pypi-publish@release/v1
      with:
        repository-url: https://test.pypi.org/legacy/
